🌟 รายงานภาพรวมระบบการเทรน
================================================================================

📊 สถิติรวมทั้งระบบ:
   จำนวนโมเดลทั้งหมด: 1
   คะแนนเฉลี่ย: 79.98/100
   Win Rate เฉลี่ย (Test): 62.88%
   Expectancy เฉลี่ย (Test): 5.00

🏆 อันดับโมเดลที่ดีที่สุด (Top 5):
   1. EURUSD M030: Score 80.0, Test W% 62.9%, Test Exp 5.00

🏗️ เปรียบเทียบตาม Architecture:
   multi_model: 1 โมเดล, Score 80.0, W% 62.9%, Exp 5.00

💰 เปรียบเทียบตาม Symbol:
   EURUSD: 1 timeframes, Score 80.0, W% 62.9%

📈 แนวโน้มการพัฒนาระบบ:
   📉 แย่ลงเฉลี่ย 3.0 คะแนน
   📊 โมเดลที่ดีขึ้น: 0/1 (0.0%)

💡 คำแนะนำสำหรับระบบ:
   ✅ ระบบมีประสิทธิภาพดี - พร้อมใช้งานจริง

📅 อัปเดตล่าสุด: 2025-09-28 15:55:24
================================================================================