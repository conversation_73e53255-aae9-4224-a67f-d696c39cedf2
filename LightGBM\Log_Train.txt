
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-28 11:36:48
🔢 จำนวนรอบทั้งหมด: 2
📊 จำนวนกลุ่มข้อมูล: 2
✅ เปิดใช้งานระบบวิเคราะห์ทางการเงิน (Account Balance: $1,000.00)
📁 จำนวนไฟล์ทั้งหมด: 14
   - M30: 7 ไฟล์
   - M60: 7 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/2
============================================================
⏰ เวลาเริ่มรอบ: 11:36:48
📊 ประมวลผลกลุ่ม M30 (7 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ AUDUSD M30
✅ สามารถเทรนได้: allowed

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ AUDUSD M30...
📁 ใช้ไฟล์: multi_asset_results_20250928_095554.json (Modified: 2025-09-28 09:55)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: AUDUSD_M30 specific

🔸 AUDUSD_M30
   Score: 52.65
   Win Rate: 39.1%
   Total Profit: $339
   Total Trades: 491
   Expectancy: 0.69
   Max Drawdown: $254
   
   Best Parameters:
     SL ATR: 2.0
     TP Ratio: 1.5
     RSI Level: 40
     Volume Spike: 1.25
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 → 2.0
   input_take_profit: 2.0 → 1.5
   input_rsi_level_in: 35 → 40
   input_volume_spike: 1.25 (unchanged)
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 → 35
   input_pull_back: 0.5 → 0.45
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/AUDUSD_M30_FIXED.csv main_round 1 symbol AUDUSD timeframe M30

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ AUDUSD MM30, ใช้ค่า default: 0.25

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M30_AUDUSD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M30_AUDUSD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ AUDUSD MM30, ใช้ค่า default: 4

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M30_AUDUSD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 4 confidence 0.25

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M30_AUDUSD_features.pkl
⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: LightGBM/Multi/models/trend_following\M30_AUDUSD_features.pkl
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ AUDUSD MM30

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ AUDUSD MM30
❌ trend_following: ไม่พบไฟล์โมเดล
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
❌ counter_trend: ไม่พบไฟล์โมเดล
❌ counter_trend_Buy: ไม่พบไฟล์โมเดล
❌ counter_trend_Sell: ไม่พบไฟล์โมเดล

📊 สรุป: 0/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
❌ ไม่พบโมเดลใดเลยสำหรับ AUDUSD MM30
❌ ไม่สามารถโหลดโมเดล Multi-Model ได้ - ใช้ Technical Analysis แทน

🏗️ เปิดใช้งาน try trade with threshold adjustment

📂 พบ threshold ที่บันทึกไว้สำหรับ AUDUSD_M30_technical
   ค่า threshold: 0.2
   จำนวน trades: 10103
   ⏱️ ข้อมูลบันทึกเมื่อ 0 วัน 0 ชั่วโมงที่แล้ว (ยังใช้ได้)

🔍 ทดสอบ threshold ที่บันทึกไว้: 0.2000

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 0.8000

📊 ใช้ Single-Model Architecture
ตรวจสอบการใช้ Model ML : False
model_features: None

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 74564 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 74514
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ AUDUSD M30
🔄 กำลังประมวลผล AUDUSD M30...
💾 บันทึกการวิเคราะห์ AUDUSD M30 ที่: Financial_Analysis_Results/AUDUSD_M30_financial_analysis.json
✅ ประมวลผล AUDUSD M30 สำเร็จ: 10125 รายการ
✅ วิเคราะห์ทางการเงิน AUDUSD M30 สำเร็จ
✅ ยืนยัน threshold ที่บันทึกไว้ใช้ได้ดี: พบ 10125 trades
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.2000 (Technical Analysis)

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 74564 ตัวอย่างข้อมูล df
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2019.07.12  06:00:00  0.69851  0.69913  0.69845  0.69893     194  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
1  2019.07.12  06:30:00  0.69893  0.69926  0.69863  0.69907     285  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
2  2019.07.12  07:00:00  0.69907  0.69974  0.69895  0.69954     311  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
3  2019.07.12  07:30:00  0.69953  0.69987  0.69946  0.69951     296  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
4  2019.07.12  08:00:00  0.69952  0.69973  0.69949  0.69965     241  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 10125 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  ATR at Entry  BB Width at Entry  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2019-07-15 08:30:00      0.70339 2019-07-15 09:00:00     0.70289        Buy   -50.0          0  ...      0.000525           0.002655       66.934814  0.000711    0.001066                272.50                      0
1 2019-07-15 10:00:00      0.70354 2019-07-15 11:00:00     0.70276        Buy   -78.0          0  ...      0.000583           0.002860       65.512218  0.001109    0.001663                279.75                      1
2 2019-07-15 12:00:00      0.70293 2019-07-15 12:00:00     0.70257        Buy   -36.0          0  ...      0.000554           0.002662       54.428358  0.000512    0.000768                373.45                      1
3 2019-07-15 12:30:00      0.70314 2019-07-15 13:30:00     0.70314        Buy     0.0          0  ...      0.000501           0.002520       57.029684  0.000000    0.001308                396.05                      1
4 2019-07-15 14:30:00      0.70319 2019-07-15 14:30:00     0.70276        Buy   -43.0          0  ...      0.000486           0.001500       54.833061  0.000611    0.000924                415.25                      0

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                30.11               
Expectancy          -27.08              
📈 สถิติสำหรับ Sell Trades:
Win%                32.04               
Expectancy          -22.45              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                31.06               
Expectancy          -24.79              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    18.34          1925                
Tuesday   19.59          2108                
Wednesday 18.60          2059                
Thursday  18.76          2036                
Friday    19.38          1997                
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         20.60          966                 
5         19.22          645                 
6         22.37          514                 
7         13.70          416                 
8         17.24          406                 
9         15.68          555                 
10        21.48          703                 
11        20.54          633                 
12        18.36          523                 
13        15.47          472                 
14        21.51          451                 
15        20.40          549                 
16        19.71          751                 
17        19.53          758                 
18        16.42          670                 
19        19.28          389                 
20        18.08          365                 
21        16.71          359                 
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2019-07-15 08:30:00
1   2019-07-15 10:00:00
2   2019-07-15 12:00:00
3   2019-07-15 12:30:00
4   2019-07-15 14:30:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2019-07-15 08:25:00
1   2019-07-15 09:55:00
2   2019-07-15 11:55:00
3   2019-07-15 12:25:00
4   2019-07-15 14:25:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 0   2019-07-12 06:00:00
1   2019-07-12 06:30:00
2   2019-07-12 07:00:00
3   2019-07-12 07:30:00
4   2019-07-12 08:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 10125
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2019-07-15 08:30:00      0.70339 2019-07-15 09:00:00     0.70289        Buy   -50.0          0           8
1 2019-07-15 10:00:00      0.70354 2019-07-15 11:00:00     0.70276        Buy   -78.0          0          10
2 2019-07-15 12:00:00      0.70293 2019-07-15 12:00:00     0.70257        Buy   -36.0          0          12
3 2019-07-15 12:30:00      0.70314 2019-07-15 13:30:00     0.70314        Buy     0.0          0          12
4 2019-07-15 14:30:00      0.70319 2019-07-15 14:30:00     0.70276        Buy   -43.0          0          14

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 10125/10125 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 952 samples (9.4%)
  Class 1 (weak_sell): 2044 samples (20.2%)
  Class 2 (no_trade): 4233 samples (41.8%)
  Class 3 (weak_buy): 1969 samples (19.4%)
  Class 4 (strong_buy): 927 samples (9.2%)
✅ Multi-class Target ถูกต้อง: 5 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0     952
1    2044
2    4233
3    1969
4     927
Name: count, dtype: int64
Class 0 (strong_sell): 952 trades, Profit range: 60.0 to 1460.0
Class 1 (weak_sell): 2044 trades, Profit range: -20.0 to 59.0
Class 2 (no_trade): 4233 trades, Profit range: -1119.0 to -21.0
Class 3 (weak_buy): 1969 trades, Profit range: -19.0 to 59.0
Class 4 (strong_buy): 927 trades, Profit range: 62.0 to 999.0
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    8229
1    1896
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    4139
1     934
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    4090
1     962
Name: count, dtype: int64
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 10125
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time     EMA50    EMA100    EMA200      RSI14
0 2019-07-15 08:30:00  0.701103  0.699682  0.698066  66.934814
1 2019-07-15 10:00:00  0.701339  0.699886  0.698218  65.512218
2 2019-07-15 12:00:00  0.701587  0.700127  0.698406  54.428358
3 2019-07-15 12:30:00  0.701642  0.700184  0.698452  57.029684
4 2019-07-15 14:30:00  0.701882  0.700421  0.698641  54.833061

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    8229
1    1896
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target               1.000000
ADX_14_Lag_1         0.024769
Volume_Lag_5         0.024305
ADX_14_Lag_2         0.024106
ADX_zone_15          0.023991
                       ...   
H2_Price_Range       0.000070
D1_MACD_signal       0.000034
RSI_Divergence_i6         NaN
RSI_Divergence_i2         NaN
RSI_Divergence_i4         NaN
Name: Target, Length: 301, dtype: float64
⚠️ เกิดข้อผิดพลาดตอนคำนวณ VIF: SVD did not converge

🔍 เริ่มการคัดเลือก features ด้วย RFE และ Feature Importance
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004063 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10277
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 62
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003691 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10274
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 61
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004915 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10271
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 60
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004832 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10016
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 59
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002952 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10014
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 58
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004747 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10012
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 57
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003969 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10010
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 56
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003932 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9755
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 55
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003761 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9753
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 54
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003918 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9750
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 53
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003433 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9747
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 52
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004346 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9744
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 51
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003254 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9741
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 50
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003378 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9738
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 49
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002179 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9735
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 48
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.001837 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9733
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 47
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003051 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9730
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 46
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003227 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9727
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 45
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002521 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9724
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 44
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003394 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9721
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 43
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918

✅ RFE เลือก 43 features จากทั้งหมด 62 features
📋 Top 10 features จาก RFE:
1. ADX_14_Lag_1
2. Volume_Lag_5
3. ADX_14_Lag_2
4. ADX_14_Lag_3
5. Volume_Lag_30
6. ATR_ROC_i8
7. ADX_14_Lag_5
8. D1_Volume_Momentum
9. Volume_MA_10
10. RSI14_x_StochD
[LightGBM] [Info] Number of positive: 1896, number of negative: 8229
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.005010 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10277
[LightGBM] [Info] Number of data points in the train set: 10125, number of used features: 62
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187259 -> initscore=-1.467918
[LightGBM] [Info] Start training from score -1.467918

✅ Feature Importance เลือก 35 features จากทั้งหมด 62 features
📋 Top 10 features จาก Feature Importance:
1. ADX_14_Lag_1
2. Volume_Lag_5
3. ADX_14_Lag_3
4. Volume_Lag_30
5. ATR_ROC_i8
6. ADX_14_Lag_5
7. Volume_MA_10
8. RSI14_x_StochD
9. STOCHk_14_3_3_Lag_3
10. EMA50_x_RollingVol5

✅ รวมทั้งสองวิธีได้ 43 features ที่สำคัญ

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 48

✅ Final selected features for training: 48 features
📋 Top 10 features:
1. Rolling_Close_5 (corr: 0.0126)
2. RSI14 (corr: 0.0104)
3. Volume_Lag_30 (corr: 0.0220)
4. EMA50_x_RollingVol5 (corr: 0.0136)
5. DMN_14_Lag_2 (corr: 0.0113)
6. Volume_Change_1 (corr: 0.0123)
7. RSI14_x_StochK (corr: 0.0112)
8. RSI_ROC_i8 (corr: 0.0113)
9. RSI14_x_StochD (corr: 0.0151)
10. ADX_14_Lag_2 (corr: 0.0241)
... และอีก 38 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\AUDUSD_M30_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\AUDUSD_M30_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 48
1. Rolling_Close_5
2. RSI14
3. Volume_Lag_30
4. EMA50_x_RollingVol5
5. DMN_14_Lag_2
6. Volume_Change_1
7. RSI14_x_StochK
8. RSI_ROC_i8
9. RSI14_x_StochD
10. ADX_14_Lag_2
11. RSI14_Lag_2
12. D1_Volume_Momentum
13. ADX_14_Lag_5
14. RSI14_x_BBwidth
15. ATR_ROC_i8
... และอีก 33 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.812741
1    0.187259
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.23
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                       count          mean           std          min           25%           50%           75%            max
Rolling_Close_5      10125.0  9.749499e-04      0.000704     0.000047      0.000563      0.000813      0.001174       0.012854
RSI14                10125.0  5.025470e+01     12.361547     8.880086     42.299806     50.429175     58.560833      88.933795
Volume_Lag_30        10125.0  1.109202e+03    916.352723    31.000000    482.000000    863.000000   1466.000000    8191.000000
EMA50_x_RollingVol5  10125.0  6.648101e-04      0.000463     0.000029      0.000386      0.000560      0.000804       0.007412
DMN_14_Lag_2         10125.0  2.153216e+01      6.950654     1.985890     16.526889     21.096043     25.852226      59.221388
Volume_Change_1      10125.0  1.162519e-01      1.563891    -0.729375     -0.168484     -0.013438      0.219020     148.750000
RSI14_x_StochK       10125.0  2.836021e+03   1911.608207     9.584875   1108.860411   2589.156725   4350.727354    8506.423932
RSI_ROC_i8           10125.0 -4.464945e-02      0.317921    -4.659851     -0.194049      0.003787      0.162206       0.739014
RSI14_x_StochD       10125.0  2.819991e+03   1837.163436    25.610817   1204.058412   2579.865457   4258.045544    8381.777914
ADX_14_Lag_2         10125.0  2.418177e+01      9.370766     6.053687     17.292664     22.395066     29.237020      69.298045
RSI14_Lag_2          10125.0  5.028989e+01     11.191697     7.264750     42.831764     50.577407     58.022603      88.275071
D1_Volume_Momentum   10125.0  4.049383e-03      1.000041    -1.000000     -1.000000      1.000000      1.000000       1.000000
ADX_14_Lag_5         10125.0  2.433367e+01      9.615897     5.899058     17.145531     22.434882     29.436831      70.570626
RSI14_x_BBwidth      10125.0  1.990569e-01      0.142866     0.026259      0.112414      0.161616      0.240187       2.185145
ATR_ROC_i8           10125.0  7.431488e-02      0.207093    -1.331633     -0.024884      0.096610      0.201342       0.735225
ADX_14_Lag_3         10125.0  2.424155e+01      9.481211     5.913954     17.241626     22.404064     29.313787      70.075907
Volume_Lag_5         10125.0  1.172286e+03    847.763278    79.000000    589.000000    950.000000   1500.000000    7539.000000
DMN_14_Lag_3         10125.0  2.147065e+01      6.933113     2.097117     16.595798     20.956032     25.843452      61.981160
H4_Price_Move        10125.0  8.231309e-05      0.001801    -0.021520     -0.000890      0.000120      0.001100       0.012550
H8_Bar_SW            10125.0  3.130864e-02      0.742211    -1.000000     -1.000000      0.000000      1.000000       1.000000
H2_Price_Move        10125.0  3.522864e-05      0.001395    -0.022490     -0.000690      0.000050      0.000780       0.013610
H4_Bar_longwick      10125.0          -inf           NaN         -inf     -0.681818     -0.112676      0.580357      26.333333
RSI14_Lag_3          10125.0  5.036381e+01     11.316972    10.146522     42.712653     50.589298     58.034999      87.607230
Volume_Lag_10        10125.0  1.031683e+03    767.751955     4.000000    488.000000    861.000000   1352.000000    7146.000000
STOCHk_14_3_3_Lag_3  10125.0  5.209551e+01     27.238648     1.618722     27.388601     53.205128     77.119184      98.805376
STOCHk_14_3_3_Lag_2  10125.0  5.180232e+01     27.132012     0.757304     27.272727     52.662037     76.700933      98.751900
Hour                 10125.0  1.147951e+01      5.206948     3.000000      7.000000     11.000000     16.000000      21.000000
Volume_MA_10         10125.0  1.174574e+03    713.847160   130.800000    663.000000   1000.500000   1484.500000    5815.300000
RSI14_x_Volume       10125.0  7.474714e+04  57524.152667  4245.976372  37218.067319  58708.989586  93956.258250  609492.611507
H2_Price_Strangth    10125.0 -1.817284e-02      0.572366    -1.000000      0.000000      0.000000      0.000000       1.000000
STOCHd_14_3_3_Lag_2  10125.0  5.199031e+01     26.280756     1.684701     28.038429     52.745598     76.033022      97.441975
RSI_ROC_i6           10125.0 -3.956118e-02      0.290175    -4.444627     -0.173920      0.003404      0.149715       0.708790
ATR_ROC_i6           10125.0  7.826465e-02      0.165820    -1.360969     -0.009825      0.086614      0.176062       0.712766
D1_Price_Range       10125.0  6.859916e-03      0.003526     0.000350      0.004670      0.006190      0.008120       0.045720
H8_Volume_Spike      10125.0  1.783704e-01      0.382843     0.000000      0.000000      0.000000      0.000000       1.000000
STOCHk_14_3_3_Lag_1  10125.0  5.140212e+01     27.272897     1.036553     26.857904     52.125280     76.101931      99.545651
DMP_14_Lag_1         10125.0  2.090913e+01      6.665795     2.469507     16.195138     20.436618     25.259389      57.700399
DMP_14_Lag_2         10125.0  2.071526e+01      6.722854     2.935945     15.955509     20.232363     25.021358      55.695432
BB_width_Lag_1       10125.0  3.895344e-03      0.002478     0.000449      0.002282      0.003287      0.004779       0.040399
H12_Price_Range      10125.0  4.786712e-03      0.002601     0.000350      0.003150      0.004220      0.005720       0.032130
STOCHd_14_3_3_Lag_1  10125.0  5.176665e+01     26.140126     1.575177     28.306116     52.553566     75.636313      98.062832
DMN_14_Lag_1         10125.0  2.180127e+01      6.967263     1.795928     16.835473     21.379834     26.043698      58.046880
ADX_14_Lag_1         10125.0  2.410271e+01      9.177532     6.864946     17.337554     22.435199     29.025319      70.894514
DayOfWeek            10125.0  2.007111e+00      1.399592     0.000000      1.000000      2.000000      3.000000       4.000000
IsMorning            10125.0  2.392099e-01      0.426622     0.000000      0.000000      0.000000      0.000000       1.000000
IsAfternoon          10125.0  2.062222e-01      0.404612     0.000000      0.000000      0.000000      0.000000       1.000000
IsEvening            10125.0  2.330864e-01      0.422818     0.000000      0.000000      0.000000      0.000000       1.000000
IsNight              10125.0  1.102222e-01      0.313182     0.000000      0.000000      0.000000      0.000000       1.000000

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
⚠️ พบ 41 คู่ features ที่มีความสัมพันธ์สูง (>0.8)

คู่ features ที่มีความสัมพันธ์สูง:
          Feature 1           Feature 2  Correlation
STOCHd_14_3_3_Lag_2 STOCHk_14_3_3_Lag_3     0.994747
STOCHk_14_3_3_Lag_3 STOCHd_14_3_3_Lag_2     0.994747
STOCHd_14_3_3_Lag_1 STOCHk_14_3_3_Lag_2     0.994435
STOCHk_14_3_3_Lag_2 STOCHd_14_3_3_Lag_1     0.994435
    Rolling_Close_5 EMA50_x_RollingVol5     0.993379
EMA50_x_RollingVol5     Rolling_Close_5     0.993379
       ADX_14_Lag_2        ADX_14_Lag_3     0.993144
       ADX_14_Lag_3        ADX_14_Lag_2     0.993144
       ADX_14_Lag_1        ADX_14_Lag_2     0.992555
       ADX_14_Lag_2        ADX_14_Lag_1     0.992555
       ADX_14_Lag_3        ADX_14_Lag_5     0.976667
       ADX_14_Lag_5        ADX_14_Lag_3     0.976667
       ADX_14_Lag_3        ADX_14_Lag_1     0.975161
       ADX_14_Lag_1        ADX_14_Lag_3     0.975161
     RSI14_x_StochK      RSI14_x_StochD     0.972709
     RSI14_x_StochD      RSI14_x_StochK     0.972709
STOCHd_14_3_3_Lag_1 STOCHd_14_3_3_Lag_2     0.955911
STOCHd_14_3_3_Lag_2 STOCHd_14_3_3_Lag_1     0.955911
       ADX_14_Lag_2        ADX_14_Lag_5     0.951761
       ADX_14_Lag_5        ADX_14_Lag_2     0.951761
STOCHd_14_3_3_Lag_2 STOCHk_14_3_3_Lag_2     0.949444
STOCHk_14_3_3_Lag_2 STOCHd_14_3_3_Lag_2     0.949444
STOCHd_14_3_3_Lag_1 STOCHk_14_3_3_Lag_3     0.945453
STOCHk_14_3_3_Lag_3 STOCHd_14_3_3_Lag_1     0.945453
STOCHd_14_3_3_Lag_1 STOCHk_14_3_3_Lag_1     0.941832
STOCHk_14_3_3_Lag_1 STOCHd_14_3_3_Lag_1     0.941832
STOCHk_14_3_3_Lag_2 STOCHk_14_3_3_Lag_3     0.938236
STOCHk_14_3_3_Lag_3 STOCHk_14_3_3_Lag_2     0.938236
     RSI14_x_StochD STOCHk_14_3_3_Lag_1     0.936745
STOCHk_14_3_3_Lag_1      RSI14_x_StochD     0.936745
STOCHk_14_3_3_Lag_1 STOCHk_14_3_3_Lag_2     0.927503
STOCHk_14_3_3_Lag_2 STOCHk_14_3_3_Lag_1     0.927503
       ADX_14_Lag_5        ADX_14_Lag_1     0.921812
       ADX_14_Lag_1        ADX_14_Lag_5     0.921812
       DMN_14_Lag_3        DMN_14_Lag_2     0.908750
       DMN_14_Lag_2        DMN_14_Lag_3     0.908750
     RSI14_x_StochD STOCHd_14_3_3_Lag_1     0.899875
STOCHd_14_3_3_Lag_1      RSI14_x_StochD     0.899875
         ATR_ROC_i8          ATR_ROC_i6     0.899034
         ATR_ROC_i6          ATR_ROC_i8     0.899034
       Volume_MA_10        Volume_Lag_5     0.892106
       Volume_Lag_5        Volume_MA_10     0.892106
        RSI14_Lag_3         RSI14_Lag_2     0.890798
        RSI14_Lag_2         RSI14_Lag_3     0.890798
       DMP_14_Lag_1        DMP_14_Lag_2     0.889575
       DMP_14_Lag_2        DMP_14_Lag_1     0.889575
     RSI14_x_StochK STOCHk_14_3_3_Lag_1     0.887808
STOCHk_14_3_3_Lag_1      RSI14_x_StochK     0.887808
       DMN_14_Lag_2        DMN_14_Lag_1     0.885551
       DMN_14_Lag_1        DMN_14_Lag_2     0.885551
         RSI_ROC_i8          RSI_ROC_i6     0.885160
         RSI_ROC_i6          RSI_ROC_i8     0.885160
STOCHk_14_3_3_Lag_2      RSI14_x_StochD     0.883789
     RSI14_x_StochD STOCHk_14_3_3_Lag_2     0.883789
              RSI14      RSI14_x_StochK     0.872787
     RSI14_x_StochK               RSI14     0.872787
              RSI14      RSI14_x_StochD     0.849278
     RSI14_x_StochD               RSI14     0.849278
       DMN_14_Lag_3         RSI14_Lag_3     0.847240
        RSI14_Lag_3        DMN_14_Lag_3     0.847240
       DMN_14_Lag_2         RSI14_Lag_2     0.844699
        RSI14_Lag_2        DMN_14_Lag_2     0.844699
        RSI14_Lag_2        DMP_14_Lag_2     0.842532
       DMP_14_Lag_2         RSI14_Lag_2     0.842532
     RSI14_x_StochD         RSI14_Lag_2     0.835181
        RSI14_Lag_2      RSI14_x_StochD     0.835181
    RSI14_x_BBwidth      BB_width_Lag_1     0.822240
     BB_width_Lag_1     RSI14_x_BBwidth     0.822240
        RSI14_Lag_2 STOCHk_14_3_3_Lag_2     0.811271
STOCHk_14_3_3_Lag_2         RSI14_Lag_2     0.811271
STOCHk_14_3_3_Lag_1 STOCHd_14_3_3_Lag_2     0.810585
STOCHd_14_3_3_Lag_2 STOCHk_14_3_3_Lag_1     0.810585
        RSI14_Lag_3 STOCHk_14_3_3_Lag_3     0.810160
STOCHk_14_3_3_Lag_3         RSI14_Lag_3     0.810160
        RSI14_Lag_3 STOCHd_14_3_3_Lag_2     0.808889
STOCHd_14_3_3_Lag_2         RSI14_Lag_3     0.808889
        RSI14_Lag_3        DMP_14_Lag_2     0.808397
       DMP_14_Lag_2         RSI14_Lag_3     0.808397
        RSI14_Lag_2 STOCHd_14_3_3_Lag_1     0.806570
STOCHd_14_3_3_Lag_1         RSI14_Lag_2     0.806570
        RSI14_Lag_3        DMN_14_Lag_2     0.802634
       DMN_14_Lag_2         RSI14_Lag_3     0.802634

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...

📊 ใช้ Target Column: Target_Multiclass
📊 การกระจายของ Target ทั้งหมด:
Target_Multiclass
2    4233
1    2044
3    1969
0     952
4     927
Name: count, dtype: int64

🔄 ใช้ Stratified Time Series Split เพื่อรักษา positive samples ใน validation set
📊 Total positive samples: 2044
📊 Total negative samples: 952
📊 Positive distribution - Train: 1228, Val: 408, Test: 408

📊 การกระจายของ Target ในชุดข้อมูลหลังแบ่ง:
Train: 1800 samples, positive: 1228 (68.2%)
Val: 598 samples, positive: 408 (68.2%)
Test: 598 samples, positive: 408 (68.2%)
Train distribution: Target_Multiclass
1    0.682222
0    0.317778
Name: proportion, dtype: float64
Val distribution: Target_Multiclass
1    0.682274
0    0.317726
Name: proportion, dtype: float64
Test distribution: Target_Multiclass
1    0.682274
0    0.317726
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338

🏗️ เปิดใช้งาน analyze time filters (Enhanced)
📊 Adaptive Thresholds - Win Rate: 0.300, Expectancy: 52.052

📊 Enhanced Time Filter Analysis for AUDUSD:
📅 Recommended Days: []
⏰ Recommended Hours: []
📈 Performance Improvement: {'error': 'No trades match the filter criteria'}
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M30_AUDUSD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
⚠️ เกิดข้อผิดพลาด ขณะทำ Feature Scaling: Input X contains infinity or a value too large for dtype('float64').
❌ เกิดข้อผิดพลาดในรอบที่ 1 กลุ่ม M30: not enough values to unpack (expected 7, got 6)
⏱️ เวลาที่ใช้: 105.1 วินาที (1.8 นาที)
📈 เฉลี่ยต่อไฟล์: 15.0 วินาที/ไฟล์
📊 ประมวลผลกลุ่ม M60 (7 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ AUDUSD M60
⚠️ เทรนเกินจำนวนที่กำหนดแล้ววันนี้ (5/3)
🚫 ไม่สามารถเทรนได้: daily_limit_exceeded
💡 ข้ามการเทรนและใช้โมเดลเดิม

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ EURUSD M60
✅ สามารถเทรนได้: allowed

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ EURUSD M60...
📁 ใช้ไฟล์: multi_asset_results_20250928_095554.json (Modified: 2025-09-28 09:55)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: EURUSD_M60 specific

🔸 EURUSD_M60
   Score: 50.98
   Win Rate: 34.4%
   Total Profit: $170
   Total Trades: 279
   Expectancy: 0.61
   Max Drawdown: $255
   
   Best Parameters:
     SL ATR: 1.0
     TP Ratio: 2.0
     RSI Level: 35
     Volume Spike: 1.25
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 2.0 → 1.0
   input_take_profit: 1.5 → 2.0
   input_rsi_level_in: 40 → 35
   input_volume_spike: 1.25 (unchanged)
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 35 (unchanged)
   input_pull_back: 0.45 (unchanged)
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/AUDUSD_H1_FIXED.csv main_round 1 symbol AUDUSD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ AUDUSD MM60, ใช้ค่า default: 0.25

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 13

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M60_AUDUSD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 13 confidence 0.25

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_AUDUSD_features.pkl
✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: 43 features
1. Volume_Change_3
2. H12_Bar_longwick
3. H12_Bar_TL
4. H8_MACD_line
5. H12_Bar_SW
6. H12_Volume_TrendStrength
7. H8_Price_Strangth
8. H8_Bar_longwick
9. D1_Price_Strangth
10. D1_Bar_FVG
... และอีก 33 features

First 5 rows of df:
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2014.04.21  00:00:00  0.93340  0.93455  0.93142  0.93256   19554  ...              -1.0              0.0                -1.0                     -1.0           1.0          -1.0            -1.0
1  2014.04.22  00:00:00  0.93262  0.93771  0.93205  0.93651   32518  ...               0.0              0.0                 1.0                     -1.0           1.0          -1.0            -1.0
2  2014.04.23  00:00:00  0.93652  0.93758  0.92666  0.92889   38857  ...               0.0              0.0                 1.0                     -1.0           1.0          -1.0            -1.0
3  2014.04.24  00:00:00  0.92886  0.92998  0.92512  0.92613   32494  ...               0.0              0.0                 1.0                      1.0           1.0          -1.0            -1.0
4  2014.04.25  00:00:00  0.92625  0.92970  0.92529  0.92786   26181  ...               1.0              0.0                -1.0                     -1.0           1.0          -1.0            -1.0

[5 rows x 332 columns]
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ AUDUSD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ AUDUSD MM60
✅ trend_following: พร้อมใช้งาน
✅ trend_following_Buy: พร้อมใช้งาน
✅ trend_following_Sell: พร้อมใช้งาน
✅ counter_trend: พร้อมใช้งาน
✅ counter_trend_Buy: พร้อมใช้งาน
✅ counter_trend_Sell: พร้อมใช้งาน

📊 สรุป: 6/6 โมเดลพร้อมใช้งาน
🔍 กำลังโหลดโมเดลสำหรับ AUDUSD MM60
📁 Base folder: LightGBM/Multi/models
🎯 Strategy: use_available
📋 จะโหลด 6 scenarios: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']

🔍 โหลด trend_following:
  📄 Model: LightGBM/Multi/models\trend_following\M60_AUDUSD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following\M60_AUDUSD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following\M60_AUDUSD_scaler.pkl
✅ โหลดโมเดล trend_following สำเร็จ
  📊 Features: 43 features

🔍 โหลด trend_following_Buy:
  📄 Model: LightGBM/Multi/models\trend_following_Buy\M60_AUDUSD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following_Buy\M60_AUDUSD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following_Buy\M60_AUDUSD_scaler.pkl
✅ โหลดโมเดล trend_following_Buy สำเร็จ
  📊 Features: 43 features

🔍 โหลด trend_following_Sell:
  📄 Model: LightGBM/Multi/models\trend_following_Sell\M60_AUDUSD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following_Sell\M60_AUDUSD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following_Sell\M60_AUDUSD_scaler.pkl
✅ โหลดโมเดล trend_following_Sell สำเร็จ
  📊 Features: 43 features

🔍 โหลด counter_trend:
  📄 Model: LightGBM/Multi/models\counter_trend\M60_AUDUSD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend\M60_AUDUSD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend\M60_AUDUSD_scaler.pkl
✅ โหลดโมเดล counter_trend สำเร็จ
  📊 Features: 43 features

🔍 โหลด counter_trend_Buy:
  📄 Model: LightGBM/Multi/models\counter_trend_Buy\M60_AUDUSD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Buy\M60_AUDUSD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Buy\M60_AUDUSD_scaler.pkl
✅ โหลดโมเดล counter_trend_Buy สำเร็จ
  📊 Features: 43 features

🔍 โหลด counter_trend_Sell:
  📄 Model: LightGBM/Multi/models\counter_trend_Sell\M60_AUDUSD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Sell\M60_AUDUSD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Sell\M60_AUDUSD_scaler.pkl
✅ โหลดโมเดล counter_trend_Sell สำเร็จ
  📊 Features: 43 features

📊 สรุปการโหลดโมเดล: 6/6 โมเดล
✅ โหลดโมเดลครบทุก scenarios
✅ โหลดโมเดล Multi-Model สำเร็จ: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following_Buy - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following_Sell - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Buy - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Sell - ใช้ค่าเริ่มต้น: 0.2500
🎯 Scenario Thresholds: {'trend_following': 0.25, 'counter_trend': 0.25, 'trend_following_Buy': 0.25, 'trend_following_Sell': 0.25, 'counter_trend_Buy': 0.25, 'counter_trend_Sell': 0.25}

🏗️ เปิดใช้งาน try trade with threshold adjustment

🔧 เริ่มทดสอบ Multi-Model ด้วย reduce_threshold เริ่มต้น 0.8 (threshold จริง: 0.2000)

============================================================
🧪 ครั้งที่ 1: ทดสอบ reduce_threshold = 0.800 (threshold จริง: 0.2000) (Multi-Model)
============================================================
ใช้ Multi-Model พร้อม scenario_thresholds

🏗️ เปิดใช้งาน create trade cycles with multi model

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  downtrend: 21905 (47.6%)
  uptrend: 20307 (44.1%)
  sideways: 3832 (8.3%)
📊 โหลด threshold สำหรับ trend_following: 0.2500
📊 โหลด threshold สำหรับ trend_following_Buy: 0.2500
📊 โหลด threshold สำหรับ trend_following_Sell: 0.2500
📊 โหลด threshold สำหรับ counter_trend: 0.2500
📊 โหลด threshold สำหรับ counter_trend_Buy: 0.2500
📊 โหลด threshold สำหรับ counter_trend_Sell: 0.2500
🎯 Scenario Thresholds: {'trend_following': 0.25, 'trend_following_Buy': 0.25, 'trend_following_Sell': 0.25, 'counter_trend': 0.25, 'counter_trend_Buy': 0.25, 'counter_trend_Sell': 0.25}
📊 ใช้ Multi-Model: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
✅ ใช้ Multi-Model Architecture พร้อม 2 scenarios

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 0.8000

🔄 ใช้ Multi-Model Architecture: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
📋 Scenario Model Mapping: {'trend_following': 'trend_following', 'counter_trend': 'counter_trend', 'trend_following_Buy': 'trend_following_Buy', 'trend_following_Sell': 'trend_following_Sell', 'counter_trend_Buy': 'counter_trend_Buy', 'counter_trend_Sell': 'counter_trend_Sell'}
🔍 ตรวจสอบโมเดลที่โหลดมาแล้ว: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
📊 Scenario trend_following ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend ใช้: ✅ โมเดลตัวเอง
📊 Scenario trend_following_Buy ใช้: ✅ โมเดลตัวเอง
📊 Scenario trend_following_Sell ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend_Buy ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend_Sell ใช้: ✅ โมเดลตัวเอง
✅ Features สะอาด: 43 features (ไม่มี raw price หรือ data leakage)

🤖 สถานะการใช้งานโมเดลตาม Scenario:
   trend_following: ✅ ใช้โมเดลตัวเอง
   counter_trend: ✅ ใช้โมเดลตัวเอง
   trend_following_Buy: ✅ ใช้โมเดลตัวเอง
   trend_following_Sell: ✅ ใช้โมเดลตัวเอง
   counter_trend_Buy: ✅ ใช้โมเดลตัวเอง
   counter_trend_Sell: ✅ ใช้โมเดลตัวเอง
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 46044 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}
