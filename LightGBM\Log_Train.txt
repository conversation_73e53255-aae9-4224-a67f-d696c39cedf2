
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
✅ Created: LightGBM/Data_Trained (Data Storage)
✅ Created: LightGBM/Hyper_Multi (Hyperparameters)
✅ Created: LightGBM/Multi_Time (Time Used Folder)
✅ Created: LightGBM/Multi (Main Multi-Model)
✅ Created: LightGBM/Multi/feature_importance (Feature Importance)
✅ Created: LightGBM/Multi/individual_performance (Performance Analysis)
✅ Created: LightGBM/Multi/models (Models Base)
✅ Created: LightGBM/Multi/results (Results)
✅ Created: LightGBM/Multi/thresholds (Thresholds)
✅ Created: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-28 14:52:39
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
✅ เปิดใช้งานระบบวิเคราะห์ทางการเงิน (Account Balance: $1,000.00)
📁 จำนวนไฟล์ทั้งหมด: 1
   - M30: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 14:52:39
📊 ประมวลผลกลุ่ม M30 (1 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ AUDUSD M30
✅ สามารถเทรนได้: allowed

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ AUDUSD M30...
📁 ใช้ไฟล์: multi_asset_results_20250928_095554.json (Modified: 2025-09-28 09:55)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: AUDUSD_M30 specific

🔸 AUDUSD_M30
   Score: 52.65
   Win Rate: 39.1%
   Total Profit: $339
   Total Trades: 491
   Expectancy: 0.69
   Max Drawdown: $254
   
   Best Parameters:
     SL ATR: 2.0
     TP Ratio: 1.5
     RSI Level: 40
     Volume Spike: 1.25
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 → 2.0
   input_take_profit: 2.0 → 1.5
   input_rsi_level_in: 35 → 40
   input_volume_spike: 1.25 (unchanged)
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 → 35
   input_pull_back: 0.5 → 0.45
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/AUDUSD_M30_FIXED.csv main_round 1 symbol AUDUSD timeframe M30

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ AUDUSD MM30, ใช้ค่า default: 0.25

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M30_AUDUSD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M30_AUDUSD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ AUDUSD MM30, ใช้ค่า default: 4

✅ ข้อมูลที่ส่งเข้า create_features : nBars_SL 4

🏗️ เปิดใช้งาน load and clean data
--- Loading and Cleaning AUDUSD_M30_FIXED.csv ---
✅ อ่านไฟล์สำเร็จด้วย : CSV_Files_Fixed/AUDUSD_M30_FIXED.csv
🔍 ตรวจสอบโครงสร้างไฟล์: CSV_Files_Fixed/AUDUSD_M30_FIXED.csv
📊 จำนวนคอลัมน์: 7
📊 Shape: (74768, 7)
📊 คอลัมน์ปัจจุบัน: ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']

🏗️ เปิดใช้งาน create features
🔍 ใช้ระบบป้องกัน Data Leakage
🔍 สร้าง safe features...
📊 คอลัมน์ที่มีอยู่: ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime']
⚠️ ไม่พบ RSI14 - ข้าม
⚠️ ไม่พบ MACD_12_26_9 - ข้าม
⚠️ ไม่พบ MACDs_12_26_9 - ข้าม
⚠️ ไม่พบ ATR - ข้าม
⚠️ ไม่พบ EMA50 - ข้าม
⚠️ ไม่พบ EMA100 - ข้าม
⚠️ ไม่พบ EMA200 - ข้าม
📊 สร้าง safe features: 4 features
📊 ลบ dangerous features: 0 features
✅ สร้าง safe features เสร็จสิ้น (74768 rows)
✅ สร้าง safe features เสร็จสิ้น
ตรวจสอบค่า : timeframe M30 timeframe_int 30
 Save_Data False Print True
🔍 ตรวจสอบ columns ข้อมูล df ก่อนเข้า combined : จำนวน 147
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'Volume_MA20_safe', 'Close_MA5_safe', 'Close_MA10_safe', 'Close_MA20_safe', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_OP5', 'EMA_OP10', 'EMA_OP15', 'EMA_CL5', 'EMA_CL10', 'EMA_CL15', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'EMA50', 'EMA100', 'EMA200', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_EMA4', 'RSI_EMA8', 'RSI_EMA12', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'EMA12', 'EMA26', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'Support_100', 'Resistance_100', 'PullBack_100_Up', 'PullBack_100_Down', 'SL_Buy', 'SL_Sell', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth']
🔍 ตรวจสอบ columns ข้อมูล df หลัง combined : จำนวน 242
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']

--- Merging MTF features ---
🔍 ตรวจสอบ columns ข้อมูล base_df ก่อนเข้า MTF features : จำนวน 241
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf

Last base_df: 2025-07-11 23:30:00
Last H2: 2025-07-11 22:00:00
Last H4: 2025-07-11 20:00:00
Last D1: 2025-07-11 00:00:00

ตรวจสอบว่า row สุดท้ายยังอยู่หลัง merge:

Last row before merge (df_combined) :              Date      Time     Open     High      Low    Close  Volume            DateTime  ...  Close_Std_5  Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
74767  2025.07.11  23:30:00  0.65724  0.65772  0.65693  0.65728     841 2025-07-11 23:30:00  ...      0.00033        633.6     0.657757      0.000285         775.1     0.657832      0.000402        1117.2

[1 rows x 242 columns]

Last row before merge (base_df) :                            Date      Time     Open     High      Low    Close  Volume  DayOfWeek  ...  Close_Std_5  Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
DateTime                                                                                          ...                                                                                                            
2025-07-11 23:30:00  2025.07.11  23:30:00  0.65724  0.65772  0.65693  0.65728     841          4  ...      0.00033        633.6     0.657757      0.000285         775.1     0.657832      0.000402        1117.2

[1 rows x 241 columns]

Last row after merge (df_merged) :                            Date      Time     Open     High      Low    Close  Volume  ...  D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
DateTime                                                                               ...                                                                                                                             
2025-07-11 23:30:00  2025.07.11  23:30:00  0.65724  0.65772  0.65693  0.65728     841  ...                0.0              0.0                -1.0                     -1.0           1.0           1.0            -1.0

[1 rows x 331 columns]

✅ Final df_merged columns: 332
Index(['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime',
       'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight',
       'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB'],
      dtype='object')
🔎 Last row:                               74767
Date                     2025.07.11
Time                       23:30:00
Open                        0.65724
High                        0.65772
Low                         0.65693
...                             ...
D1_Volume_Momentum             -1.0
D1_Volume_TrendStrength        -1.0
D1_MACD_line                    1.0
D1_MACD_deep                    1.0
D1_MACD_signal                 -1.0

[332 rows x 1 columns]
🔍 ตรวจสอบ columns ข้อมูล df หลัง mtf : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป 204 จาก 74768 แถว)

🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา
- ข้อมูลเรียงตามเวลา: ใช่ (ควรเป็น 'ใช่')
- ช่วงเวลาข้อมูล: 2019-07-12 06:00:00 ถึง 2025-07-11 23:30:00
- ระยะเวลารวม: 2191 days 17:30:00
- ช่วงห่างระหว่างบันทึก (เฉลี่ย): 0 days 00:42:19.669809422
- ช่วงห่างระหว่างบันทึก (สูงสุด): 3 days 05:30:00
- ช่วงห่างระหว่างบันทึก (ต่ำสุด): 0 days 00:30:00
- จำนวนช่วงเวลาที่หายไป: 317 (จากทั้งหมด 74563 ช่วง)
⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์
- จำนวน timestamp ที่ซ้ำกัน: 0

🔍 ตรวจสอบ Stationarity ของข้อมูล:

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Close:
ADF Statistic: -2.0336
p-value: 0.2720
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Returns:
ADF Statistic: -35.5507
p-value: 0.0000
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

💾 บันทึกรายงาน Temporal Analysis ที่: LightGBM/Multi/results\M30_AUDUSD_temporal_report.json

📌 จำนวน Missing Values แสดงเฉพาะคอลัมน์ที่มีค่าว่าง และจำนวน > 0:
Series([], dtype: int64)

📌 จำนวน Missing Values หลังการประมวลผล:
Series([], dtype: int64)

🔍 Unique values in df['DayOfWeek']: [4 0 1 2 3 6]

🏗️ เปิดใช้งาน check data quality
==================================================
Data Quality Check for CSV_Files_Fixed/AUDUSD_M30_FIXED.csv
==================================================

[4] Duplicate Rows: 0
💾 บันทึก df_with_features ลงไฟล์ LightGBM/Data_Trained/M30_AUDUSD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 4 confidence 0.25

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M30_AUDUSD_features.pkl
⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: LightGBM/Multi/models/trend_following\M30_AUDUSD_features.pkl
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ AUDUSD MM30

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ AUDUSD MM30
❌ trend_following: ไม่พบไฟล์โมเดล
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
❌ counter_trend: ไม่พบไฟล์โมเดล
❌ counter_trend_Buy: ไม่พบไฟล์โมเดล
❌ counter_trend_Sell: ไม่พบไฟล์โมเดล

📊 สรุป: 0/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
❌ ไม่พบโมเดลใดเลยสำหรับ AUDUSD MM30
❌ ไม่สามารถโหลดโมเดล Multi-Model ได้ - ใช้ Technical Analysis แทน

🏗️ เปิดใช้งาน try trade with threshold adjustment

📂 พบ threshold ที่บันทึกไว้สำหรับ AUDUSD_M30_technical
   ค่า threshold: 0.2
   จำนวน trades: 10103
   ⏱️ ข้อมูลบันทึกเมื่อ 0 วัน 2 ชั่วโมงที่แล้ว (ยังใช้ได้)

🔍 ทดสอบ threshold ที่บันทึกไว้: 0.2000

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 0.8000

📊 ใช้ Single-Model Architecture
ตรวจสอบการใช้ Model ML : False
model_features: None

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 204 (ข้อมูลทั้งหมด 74564 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 74360
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ AUDUSD M30
🔄 กำลังประมวลผล AUDUSD M30...
💾 บันทึกการวิเคราะห์ AUDUSD M30 ที่: Financial_Analysis_Results/AUDUSD_M30_financial_analysis.json
✅ ประมวลผล AUDUSD M30 สำเร็จ: 10103 รายการ
✅ วิเคราะห์ทางการเงิน AUDUSD M30 สำเร็จ
✅ ยืนยัน threshold ที่บันทึกไว้ใช้ได้ดี: พบ 10103 trades
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.2000 (Technical Analysis)

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 74564 ตัวอย่างข้อมูล df
           Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
204  2019.07.12  06:00:00  0.69851  0.69913  0.69845  0.69893     194  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
205  2019.07.12  06:30:00  0.69893  0.69926  0.69863  0.69907     285  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
206  2019.07.12  07:00:00  0.69907  0.69974  0.69895  0.69954     311  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
207  2019.07.12  07:30:00  0.69953  0.69987  0.69946  0.69951     296  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
208  2019.07.12  08:00:00  0.69952  0.69973  0.69949  0.69965     241  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 10103 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  ATR at Entry  BB Width at Entry  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2019-07-18 13:30:00      0.70323 2019-07-18 13:30:00     0.70277        Buy   -46.0          3  ...      0.000460           0.003073       58.424730  0.000654    0.000981                505.95                      0
1 2019-07-18 14:30:00      0.70343 2019-07-18 15:30:00     0.70343        Buy     0.0          3  ...      0.000506           0.001871       61.350369  0.000000    0.001791                579.10                      0
2 2019-07-18 17:00:00      0.70353 2019-07-18 21:00:00     0.70467        Buy   114.0          3  ...      0.000580           0.001121       57.178543  0.000000    0.001620                697.75                      1
3 2019-07-18 21:30:00      0.70600 2019-07-19 01:00:00     0.70600        Buy     0.0          3  ...      0.000646           0.002642       76.290329  0.000000    0.002762                803.90                      1
4 2019-07-19 04:00:00      0.70699 2019-07-19 05:00:00     0.70699        Buy     0.0          4  ...      0.000931           0.005363       63.032152  0.000000    0.002843                629.20                      0

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                30.19               
Expectancy          -27.00              
📈 สถิติสำหรับ Sell Trades:
Win%                32.06               
Expectancy          -22.41              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                31.11               
Expectancy          -24.73              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    18.35          1918                
Tuesday   19.66          2101                
Wednesday 18.66          2053                
Thursday  18.73          2034                
Friday    19.38          1997                
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         20.64          964                 
5         19.13          643                 
6         22.46          512                 
7         13.73          415                 
8         17.28          405                 
9         15.68          555                 
10        21.54          701                 
11        20.60          631                 
12        18.43          521                 
13        15.47          472                 
14        21.56          450                 
15        20.51          546                 
16        19.73          750                 
17        19.53          758                 
18        16.32          668                 
19        19.28          389                 
20        18.13          364                 
21        16.71          359                 
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2019-07-18 13:30:00
1   2019-07-18 14:30:00
2   2019-07-18 17:00:00
3   2019-07-18 21:30:00
4   2019-07-19 04:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2019-07-18 13:25:00
1   2019-07-18 14:25:00
2   2019-07-18 16:55:00
3   2019-07-18 21:25:00
4   2019-07-19 03:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 204   2019-07-12 06:00:00
205   2019-07-12 06:30:00
206   2019-07-12 07:00:00
207   2019-07-12 07:30:00
208   2019-07-12 08:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 10103
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2019-07-18 13:30:00      0.70323 2019-07-18 13:30:00     0.70277        Buy   -46.0          3          13
1 2019-07-18 14:30:00      0.70343 2019-07-18 15:30:00     0.70343        Buy     0.0          3          14
2 2019-07-18 17:00:00      0.70353 2019-07-18 21:00:00     0.70467        Buy   114.0          3          17
3 2019-07-18 21:30:00      0.70600 2019-07-19 01:00:00     0.70600        Buy     0.0          3          21
4 2019-07-19 04:00:00      0.70699 2019-07-19 05:00:00     0.70699        Buy     0.0          4           4

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 10103/10103 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 952 samples (9.4%)
  Class 1 (weak_sell): 2043 samples (20.2%)
  Class 2 (no_trade): 4218 samples (41.7%)
  Class 3 (weak_buy): 1965 samples (19.4%)
  Class 4 (strong_buy): 925 samples (9.2%)
✅ Multi-class Target ถูกต้อง: 5 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0     952
1    2043
2    4218
3    1965
4     925
Name: count, dtype: int64
Class 0 (strong_sell): 952 trades, Profit range: 60.0 to 1460.0
Class 1 (weak_sell): 2043 trades, Profit range: -20.0 to 59.0
Class 2 (no_trade): 4218 trades, Profit range: -1119.0 to -21.0
Class 3 (weak_buy): 1965 trades, Profit range: -19.0 to 59.0
Class 4 (strong_buy): 925 trades, Profit range: 62.0 to 999.0
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    8209
1    1894
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    4122
1     932
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    4087
1     962
Name: count, dtype: int64
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 10103
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time     EMA50    EMA100    EMA200      RSI14
0 2019-07-18 13:30:00  0.702148  0.701891  0.701217  58.424730
1 2019-07-18 14:30:00  0.702228  0.701942  0.701256  61.350369
2 2019-07-18 17:00:00  0.702435  0.702079  0.701359  57.178543
3 2019-07-18 21:30:00  0.702994  0.702439  0.701609  76.290329
4 2019-07-19 04:00:00  0.704515  0.703429  0.702238  63.032152

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    8209
1    1894
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target               1.000000
Volume_Lag_5         0.024899
ADX_14_Lag_1         0.024462
ADX_zone_15          0.023799
ADX_14_Lag_2         0.023778
                       ...   
D1_MACD_signal       0.000046
ATR_ROC_i2           0.000018
RSI_Divergence_i4         NaN
RSI_Divergence_i6         NaN
RSI_Divergence_i2         NaN
Name: Target, Length: 301, dtype: float64
⚠️ เกิดข้อผิดพลาดตอนคำนวณ VIF: SVD did not converge

🔍 เริ่มการคัดเลือก features ด้วย RFE และ Feature Importance
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004762 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10538
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 65
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003860 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10283
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 64
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002691 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10281
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 63
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004652 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10279
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 62
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003879 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10276
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 61
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002646 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10273
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 60
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003785 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10270
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 59
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004007 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10268
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 58
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003918 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10266
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 57
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003976 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10263
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 56
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004015 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10260
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 55
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003589 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10257
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 54
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003626 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10254
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 53
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003489 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10251
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 52
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003648 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10248
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 51
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003524 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10246
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 50
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003171 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9991
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 49
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003034 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9988
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 48
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003351 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9986
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 47
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003719 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9983
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 46
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002901 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 9980
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 45
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540

✅ RFE เลือก 45 features จากทั้งหมด 65 features
📋 Top 10 features จาก RFE:
1. Volume_Lag_5
2. ADX_14_Lag_1
3. ADX_14_Lag_2
4. Volume_Lag_30
5. ADX_14_Lag_3
6. ATR_ROC_i8
7. ADX_14_Lag_5
8. H2_Bar_OSB
9. D1_Volume_Momentum
10. Volume_MA_10
[LightGBM] [Info] Number of positive: 1894, number of negative: 8209
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.005428 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 10538
[LightGBM] [Info] Number of data points in the train set: 10103, number of used features: 65
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.187469 -> initscore=-1.466540
[LightGBM] [Info] Start training from score -1.466540

✅ Feature Importance เลือก 35 features จากทั้งหมด 65 features
📋 Top 10 features จาก Feature Importance:
1. Volume_Lag_5
2. Volume_Lag_30
3. ATR_ROC_i8
4. ADX_14_Lag_5
5. Volume_MA_10
6. RSI14_x_StochD
7. STOCHk_14_3_3_Lag_2
8. STOCHk_14_3_3_Lag_3
9. RSI14_x_BBwidth
10. EMA50_x_RollingVol5

✅ รวมทั้งสองวิธีได้ 45 features ที่สำคัญ

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 50

✅ Final selected features for training: 50 features
📋 Top 10 features:
1. Volume_MA_10 (corr: 0.0171)
2. EMA50_x_RollingVol5 (corr: 0.0133)
3. STOCHk_14_3_3_Lag_2 (corr: 0.0146)
4. STOCHd_14_3_3_Lag_1 (corr: 0.0143)
5. DMN_14_Lag_1 (corr: 0.0118)
6. RSI_ROC_i6 (corr: 0.0132)
7. ADX_14_Lag_3 (corr: 0.0221)
8. RSI14 (corr: 0.0108)
9. Close_Std_20 (corr: 0.0107)
10. RSI14_x_BBwidth (corr: 0.0135)
... และอีก 40 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\AUDUSD_M30_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\AUDUSD_M30_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 50
1. Volume_MA_10
2. EMA50_x_RollingVol5
3. STOCHk_14_3_3_Lag_2
4. STOCHd_14_3_3_Lag_1
5. DMN_14_Lag_1
6. RSI_ROC_i6
7. ADX_14_Lag_3
8. RSI14
9. Close_Std_20
10. RSI14_x_BBwidth
11. ATR_ROC_i6
12. Rolling_Close_5
13. RSI14_x_StochK
14. DMP_14_Lag_2
15. Close_Std_10
... และอีก 35 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.812531
1    0.187469
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.23
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                       count          mean           std          min           25%           50%           75%            max
Volume_MA_10         10103.0  1.176215e+03    713.692718   130.800000    664.300000   1002.100000   1486.350000    5815.300000
EMA50_x_RollingVol5  10103.0  6.655583e-04      0.000463     0.000029      0.000387      0.000560      0.000805       0.007412
STOCHk_14_3_3_Lag_2  10103.0  5.182216e+01     27.126937     0.757304     27.318728     52.690972     76.702663      98.751900
STOCHd_14_3_3_Lag_1  10103.0  5.178702e+01     26.137535     1.575177     28.377361     52.592593     75.636382      98.062832
DMN_14_Lag_1         10103.0  2.179662e+01      6.969760     1.795928     16.830138     21.367372     26.041348      58.046880
RSI_ROC_i6           10103.0 -3.962770e-02      0.290333    -4.444627     -0.174005      0.003514      0.149728       0.708790
ADX_14_Lag_3         10103.0  2.424308e+01      9.487115     5.913954     17.238781     22.402482     29.324433      70.075907
RSI14                10103.0  5.025576e+01     12.365923     8.880086     42.298980     50.432556     58.563728      88.933795
Close_Std_20         10103.0  9.750847e-04      0.000620     0.000112      0.000573      0.000824      0.001195       0.010100
RSI14_x_BBwidth      10103.0  1.993133e-01      0.142906     0.026259      0.112579      0.161873      0.240359       2.185145
ATR_ROC_i6           10103.0  7.820381e-02      0.165793    -1.360969     -0.009894      0.086559      0.175963       0.712766
Rolling_Close_5      10103.0  9.760766e-04      0.000705     0.000047      0.000564      0.000814      0.001176       0.012854
RSI14_x_StochK       10103.0  2.836967e+03   1912.091230     9.584875   1109.542105   2590.071834   4353.058413    8506.423932
DMP_14_Lag_2         10103.0  2.071857e+01      6.725313     2.935945     15.957075     20.232363     25.025437      55.695432
Close_Std_10         10103.0  7.525020e-04      0.000503     0.000083      0.000437      0.000633      0.000915       0.008621
ADX_14_Lag_1         10103.0  2.410490e+01      9.183611     6.864946     17.335385     22.435199     29.037898      70.894514
STOCHd_14_3_3_Lag_2  10103.0  5.200877e+01     26.275590     1.684701     28.096995     52.791290     76.051651      97.441975
Volume_MA_5          10103.0  1.295581e+03    805.032441   120.200000    730.900000   1084.200000   1638.900000    6653.400000
RSI14_x_StochD       10103.0  2.821238e+03   1837.557801    25.610817   1204.504325   2582.991962   4260.075045    8381.777914
STOCHk_14_3_3_Lag_3  10103.0  5.211439e+01     27.232048     1.618722     27.435539     53.227233     77.122198      98.805376
Volume_Lag_10        10103.0  1.033070e+03    767.933500     4.000000    489.000000    862.000000   1353.000000    7146.000000
STOCHk_14_3_3_Lag_1  10103.0  5.142450e+01     27.273302     1.036553     26.879234     52.173913     76.130577      99.545651
DMP_14_Lag_1         10103.0  2.091339e+01      6.666608     2.469507     16.202692     20.439019     25.260842      57.700399
Volume_Change_1      10103.0  1.164458e-01      1.565505    -0.729375     -0.168410     -0.013438      0.219022     148.750000
ATR_ROC_i8           10103.0  7.432237e-02      0.207108    -1.331633     -0.024766      0.096604      0.201402       0.735225
ADX_14_Lag_5         10103.0  2.433427e+01      9.621538     5.899058     17.141275     22.434882     29.443993      70.570626
RSI14_x_Volume       10103.0  7.485130e+04  57540.057644  4245.976372  37331.499639  58825.351088  94044.593662  609492.611507
RSI14_Lag_3          10103.0  5.036911e+01     11.317367    10.146522     42.742111     50.592088     58.048645      87.607230
H2_Price_Strangth    10103.0 -1.801445e-02      0.572304    -1.000000      0.000000      0.000000      0.000000       1.000000
ADX_14_Lag_2         10103.0  2.418384e+01      9.376741     6.053687     17.292014     22.391811     29.246376      69.298045
H2_Price_Move        10103.0  3.550431e-05      0.001397    -0.022490     -0.000700      0.000050      0.000790       0.013610
RSI14_Lag_2          10103.0  5.029688e+01     11.193129     7.264750     42.840126     50.590192     58.026066      88.275071
DMN_14_Lag_3         10103.0  2.146681e+01      6.936733     2.097117     16.594789     20.947632     25.840719      61.981160
Volume_Lag_5         10103.0  1.173997e+03    847.815728    79.000000    590.000000    951.000000   1503.000000    7539.000000
H4_Bar_longwick      10103.0          -inf           NaN         -inf     -0.682819     -0.113402      0.580501      26.333333
DMN_14_Lag_2         10103.0  2.152755e+01      6.952602     1.985890     16.520109     21.083772     25.852147      59.221388
H4_Price_Move        10103.0  8.271405e-05      0.001803    -0.021520     -0.000890      0.000120      0.001100       0.012550
Volume_Lag_30        10103.0  1.110639e+03    916.692402    31.000000    483.000000    864.000000   1469.000000    8191.000000
DMP_14_Lag_3         10103.0  2.067057e+01      6.799459     1.974325     15.833532     20.220692     24.995785      51.637261
RSI_ROC_i8           10103.0 -4.482749e-02      0.318144    -4.659851     -0.194209      0.003745      0.162305       0.739014
H8_Bar_SW            10103.0  3.127784e-02      0.742155    -1.000000     -1.000000      0.000000      1.000000       1.000000
Hour                 10103.0  1.148164e+01      5.207022     3.000000      7.000000     11.000000     16.000000      21.000000
D1_Volume_Momentum   10103.0  5.048006e-03      1.000037    -1.000000     -1.000000      1.000000      1.000000       1.000000
H2_Bar_OSB           10103.0 -2.177571e-03      0.393467    -1.000000      0.000000      0.000000      0.000000       1.000000
H12_Price_Strangth   10103.0 -2.345838e-02      0.614405    -1.000000      0.000000      0.000000      0.000000       1.000000
DayOfWeek            10103.0  2.009007e+00      1.399797     0.000000      1.000000      2.000000      3.000000       4.000000
IsMorning            10103.0  2.391369e-01      0.426578     0.000000      0.000000      0.000000      0.000000       1.000000
IsAfternoon          10103.0  2.061764e-01      0.404579     0.000000      0.000000      0.000000      0.000000       1.000000
IsEvening            10103.0  2.331981e-01      0.422888     0.000000      0.000000      0.000000      0.000000       1.000000
IsNight              10103.0  1.102643e-01      0.313234     0.000000      0.000000      0.000000      0.000000       1.000000

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
⚠️ พบ 45 คู่ features ที่มีความสัมพันธ์สูง (>0.8)

คู่ features ที่มีความสัมพันธ์สูง:
          Feature 1           Feature 2  Correlation
STOCHk_14_3_3_Lag_3 STOCHd_14_3_3_Lag_2     0.994747
STOCHd_14_3_3_Lag_2 STOCHk_14_3_3_Lag_3     0.994747
STOCHk_14_3_3_Lag_2 STOCHd_14_3_3_Lag_1     0.994441
STOCHd_14_3_3_Lag_1 STOCHk_14_3_3_Lag_2     0.994441
EMA50_x_RollingVol5     Rolling_Close_5     0.993371
    Rolling_Close_5 EMA50_x_RollingVol5     0.993371
       ADX_14_Lag_2        ADX_14_Lag_3     0.993153
       ADX_14_Lag_3        ADX_14_Lag_2     0.993153
       ADX_14_Lag_1        ADX_14_Lag_2     0.992565
       ADX_14_Lag_2        ADX_14_Lag_1     0.992565
       ADX_14_Lag_5        ADX_14_Lag_3     0.976674
       ADX_14_Lag_3        ADX_14_Lag_5     0.976674
       ADX_14_Lag_1        ADX_14_Lag_3     0.975192
       ADX_14_Lag_3        ADX_14_Lag_1     0.975192
     RSI14_x_StochK      RSI14_x_StochD     0.972777
     RSI14_x_StochD      RSI14_x_StochK     0.972777
STOCHd_14_3_3_Lag_1 STOCHd_14_3_3_Lag_2     0.955926
STOCHd_14_3_3_Lag_2 STOCHd_14_3_3_Lag_1     0.955926
       ADX_14_Lag_5        ADX_14_Lag_2     0.951780
       ADX_14_Lag_2        ADX_14_Lag_5     0.951780
STOCHd_14_3_3_Lag_2 STOCHk_14_3_3_Lag_2     0.949474
STOCHk_14_3_3_Lag_2 STOCHd_14_3_3_Lag_2     0.949474
STOCHd_14_3_3_Lag_1 STOCHk_14_3_3_Lag_3     0.945494
STOCHk_14_3_3_Lag_3 STOCHd_14_3_3_Lag_1     0.945494
STOCHd_14_3_3_Lag_1 STOCHk_14_3_3_Lag_1     0.941901
STOCHk_14_3_3_Lag_1 STOCHd_14_3_3_Lag_1     0.941901
STOCHk_14_3_3_Lag_3 STOCHk_14_3_3_Lag_2     0.938290
STOCHk_14_3_3_Lag_2 STOCHk_14_3_3_Lag_3     0.938290
     RSI14_x_StochD STOCHk_14_3_3_Lag_1     0.936757
STOCHk_14_3_3_Lag_1      RSI14_x_StochD     0.936757
STOCHk_14_3_3_Lag_1 STOCHk_14_3_3_Lag_2     0.927580
STOCHk_14_3_3_Lag_2 STOCHk_14_3_3_Lag_1     0.927580
       Volume_MA_10         Volume_MA_5     0.927299
        Volume_MA_5        Volume_MA_10     0.927299
       ADX_14_Lag_1        ADX_14_Lag_5     0.921856
       ADX_14_Lag_5        ADX_14_Lag_1     0.921856
       DMP_14_Lag_2        DMP_14_Lag_3     0.912615
       DMP_14_Lag_3        DMP_14_Lag_2     0.912615
       DMN_14_Lag_2        DMN_14_Lag_3     0.908881
       DMN_14_Lag_3        DMN_14_Lag_2     0.908881
STOCHd_14_3_3_Lag_1      RSI14_x_StochD     0.900043
     RSI14_x_StochD STOCHd_14_3_3_Lag_1     0.900043
         ATR_ROC_i8          ATR_ROC_i6     0.899202
         ATR_ROC_i6          ATR_ROC_i8     0.899202
       Volume_Lag_5        Volume_MA_10     0.891882
       Volume_MA_10        Volume_Lag_5     0.891882
        RSI14_Lag_2         RSI14_Lag_3     0.890833
        RSI14_Lag_3         RSI14_Lag_2     0.890833
       DMP_14_Lag_1        DMP_14_Lag_2     0.889628
       DMP_14_Lag_2        DMP_14_Lag_1     0.889628
     RSI14_x_StochK STOCHk_14_3_3_Lag_1     0.887978
STOCHk_14_3_3_Lag_1      RSI14_x_StochK     0.887978
       DMN_14_Lag_1        DMN_14_Lag_2     0.885602
       DMN_14_Lag_2        DMN_14_Lag_1     0.885602
         RSI_ROC_i8          RSI_ROC_i6     0.885302
         RSI_ROC_i6          RSI_ROC_i8     0.885302
     RSI14_x_StochD STOCHk_14_3_3_Lag_2     0.884018
STOCHk_14_3_3_Lag_2      RSI14_x_StochD     0.884018
     RSI14_x_StochK               RSI14     0.872849
              RSI14      RSI14_x_StochK     0.872849
       DMP_14_Lag_3         RSI14_Lag_3     0.852530
        RSI14_Lag_3        DMP_14_Lag_3     0.852530
       Volume_Lag_5         Volume_MA_5     0.851628
        Volume_MA_5        Volume_Lag_5     0.851628
              RSI14      RSI14_x_StochD     0.849533
     RSI14_x_StochD               RSI14     0.849533
        RSI14_Lag_3        DMN_14_Lag_3     0.847419
       DMN_14_Lag_3         RSI14_Lag_3     0.847419
       DMN_14_Lag_2         RSI14_Lag_2     0.844721
        RSI14_Lag_2        DMN_14_Lag_2     0.844721
        RSI14_Lag_2        DMP_14_Lag_2     0.842545
       DMP_14_Lag_2         RSI14_Lag_2     0.842545
     RSI14_x_StochD         RSI14_Lag_2     0.835069
        RSI14_Lag_2      RSI14_x_StochD     0.835069
       Close_Std_20     RSI14_x_BBwidth     0.821957
    RSI14_x_BBwidth        Close_Std_20     0.821957
        RSI14_Lag_2 STOCHk_14_3_3_Lag_2     0.811455
STOCHk_14_3_3_Lag_2         RSI14_Lag_2     0.811455
STOCHk_14_3_3_Lag_1 STOCHd_14_3_3_Lag_2     0.810730
STOCHd_14_3_3_Lag_2 STOCHk_14_3_3_Lag_1     0.810730
        RSI14_Lag_3 STOCHk_14_3_3_Lag_3     0.810322
STOCHk_14_3_3_Lag_3         RSI14_Lag_3     0.810322
        RSI14_Lag_3 STOCHd_14_3_3_Lag_2     0.809012
STOCHd_14_3_3_Lag_2         RSI14_Lag_3     0.809012
       DMP_14_Lag_2         RSI14_Lag_3     0.808437
        RSI14_Lag_3        DMP_14_Lag_2     0.808437
        RSI14_Lag_2 STOCHd_14_3_3_Lag_1     0.806701
STOCHd_14_3_3_Lag_1         RSI14_Lag_2     0.806701
        RSI14_Lag_3        DMN_14_Lag_2     0.802948
       DMN_14_Lag_2         RSI14_Lag_3     0.802948

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...

📊 ใช้ Target Column: Target_Multiclass
📊 การกระจายของ Target ทั้งหมด:
Target_Multiclass
2    4218
1    2043
3    1965
0     952
4     925
Name: count, dtype: int64

🔄 ใช้ Stratified Time Series Split เพื่อรักษา positive samples ใน validation set
📊 Total positive samples: 2043
📊 Total negative samples: 952
📊 Positive distribution - Train: 1227, Val: 408, Test: 408

📊 การกระจายของ Target ในชุดข้อมูลหลังแบ่ง:
Train: 1799 samples, positive: 1227 (68.2%)
Val: 598 samples, positive: 408 (68.2%)
Test: 598 samples, positive: 408 (68.2%)
Train distribution: Target_Multiclass
1    0.682046
0    0.317954
Name: proportion, dtype: float64
Val distribution: Target_Multiclass
1    0.682274
0    0.317726
Name: proportion, dtype: float64
Test distribution: Target_Multiclass
1    0.682274
0    0.317726
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338

🏗️ เปิดใช้งาน analyze time filters (Enhanced)
📊 Adaptive Thresholds - Win Rate: 0.300, Expectancy: 52.052

📊 Enhanced Time Filter Analysis for AUDUSD:
📅 Recommended Days: []
⏰ Recommended Hours: []
📈 Performance Improvement: {'error': 'No trades match the filter criteria'}
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M30_AUDUSD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
🔍 ตรวจสอบข้อมูลก่อน Feature Scaling...
X_train shape: (1799, 50)
⚠️ เกิดข้อผิดพลาด ขณะทำ Feature Scaling: Input X contains infinity or a value too large for dtype('float64').
train_data : None
val_data : None
test_data : None
df : None
trade_df : None
stats : None
❌ เกิดข้อผิดพลาดในรอบที่ 1 กลุ่ม M30: object of type 'NoneType' has no len()
⏱️ เวลาที่ใช้: 242.4 วินาที (4.0 นาที)
📈 เฉลี่ยต่อไฟล์: 242.4 วินาที/ไฟล์

────────────────────────────────────────────────────────────
📋 สรุปรอบที่ 1:
   ⏱️ เวลาที่ใช้: 242.4 วินาที (4.0 นาที)
   ✅ ไฟล์สำเร็จ: 0
   ❌ ไฟล์ผิดพลาด: 1
   ⏰ เวลาสิ้นสุด: 14:56:41
   📊 M30: 242.4s (242.4s/ไฟล์)

============================================================
⏭️ ข้ามการวิเคราะห์ Feature Importance ข้าม Assets
============================================================
📋 เหตุผล: ไม่มีผลลัพธ์การเทรน
💡 หมายเหตุ: การวิเคราะห์จะทำงานเฉพาะเมื่อเทรนโมเดลใหม่และมีผลลัพธ์
============================================================

================================================================================
🎉 สรุปการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-28 14:52:39
⏰ เวลาสิ้นสุด: 2025-09-28 14:56:41
⏱️ เวลาที่ใช้ทั้งหมด: 242.4 วินาที (4.0 นาที)

🚀 ประสิทธิภาพการทำงาน:
   📈 ประมวลผลได้: 15 ไฟล์/ชั่วโมง
================================================================================
💾 บันทึกผลการทดสอบลงใน 'LightGBM/Multi_Time\time_test.txt' เรียบร้อยแล้ว

================================================================================
💰 เริ่มการวิเคราะห์ทางการเงินรวม
================================================================================

================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินทั้งหมด
================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์
📊 พบข้อมูลการเทรด: 10103 รายการ
📊 บันทึกกราฟที่: Financial_Analysis_Results/trading_performance_analysis.png
💾 บันทึกการวิเคราะห์สมบูรณ์:
   📄 Summary: Financial_Analysis_Results/complete_financial_analysis.json
   📊 Risk Table: Financial_Analysis_Results/risk_management_table.csv
   📝 Report: Financial_Analysis_Results/financial_analysis_report.txt

============================================================
📈 FINANCIAL ANALYSIS SUMMARY
============================================================
💰 Account Balance: $1,000.00
📊 Total Trades: 10103
💵 Total Profit (1.0 lot): $-152,289.00
📉 Max Drawdown (1.0 lot): $153,603.00
🎯 Recommended Lot Size: 0.0001
⚠️ Max Risk: 2.00%
============================================================

🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
📁 ผลลัพธ์บันทึกที่: Financial_Analysis_Results

📈 สรุปผลการวิเคราะห์:
   💰 ยอดเงินในบัญชี: $1,000.00
   📊 จำนวนการเทรดทั้งหมด: 10103
   💵 กำไรรวม (1.0 lot): $-152,289.00
   📉 Drawdown สูงสุด (1.0 lot): $153,603.00
   🎯 ขนาดล็อตที่แนะนำ: 0.0001
   ⚠️ ความเสี่ยงสูงสุด: 2.00%
🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
